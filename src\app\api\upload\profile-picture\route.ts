import { v2 as cloudinary } from 'cloudinary'
import { NextRequest, NextResponse } from 'next/server'

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || '',
  api_key: process.env.CLOUDINARY_API_KEY || '',
  api_secret: process.env.CLOUDINARY_API_SECRET || '',
})

export async function POST(request: NextRequest) {
  try {
    // Check Cloudinary configuration
    if (!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || !process.env.CLOUDINARY_API_KEY || !process.env.CLOUDINARY_API_SECRET) {
      console.error('Missing Cloudinary configuration:', {
        cloudName: !!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
        apiKey: !!process.env.CLOUDINARY_API_KEY,
        apiSecret: !!process.env.CLOUDINARY_API_SECRET
      })
      return NextResponse.json({ error: 'Server configuration error: Missing Cloudinary credentials' }, { status: 500 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' },
        { status: 400 }
      )
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File too large. Maximum size is 5MB.' },
        { status: 400 }
      )
    }

    // Convert file to buffer for Cloudinary upload
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Create unique public_id for Cloudinary
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const publicId = `customer_profiles/profile_${timestamp}_${randomString}`

    // Upload to Cloudinary with enhanced quality settings
    const uploadResult = await new Promise((resolve, reject) => {
      cloudinary.uploader.upload_stream(
        {
          resource_type: 'image',
          public_id: publicId,
          folder: 'customer_profiles',
          // Enhanced transformations for better quality
          transformation: [
            // Primary transformation for high quality
            {
              width: 800,
              height: 800,
              crop: 'fill',
              gravity: 'face:center',
              quality: 95,
              format: 'webp',
              flags: 'progressive'
            }
          ],
          // Generate multiple responsive variants
          eager: [
            // Small size for thumbnails (160x160)
            {
              width: 160,
              height: 160,
              crop: 'fill',
              gravity: 'face:center',
              quality: 90,
              format: 'webp',
              transformation: 'c_fill,g_face:center,q_90,f_webp'
            },
            // Medium size for cards (320x320)
            {
              width: 320,
              height: 320,
              crop: 'fill',
              gravity: 'face:center',
              quality: 92,
              format: 'webp',
              transformation: 'c_fill,g_face:center,q_92,f_webp'
            },
            // Large size for modals (640x640)
            {
              width: 640,
              height: 640,
              crop: 'fill',
              gravity: 'face:center',
              quality: 95,
              format: 'webp',
              transformation: 'c_fill,g_face:center,q_95,f_webp'
            }
          ],
          // Additional optimization settings
          use_filename: false,
          unique_filename: true,
          overwrite: false,
          invalidate: true,
          // Face detection for better cropping
          detection: 'adv_face'
        },
        (error, result) => {
          if (error) {
            console.error('Cloudinary upload error:', error)
            reject(new Error(`Cloudinary upload failed: ${error.message}`))
          } else if (result) {
            resolve(result)
          } else {
            reject(new Error('Upload failed: No result returned from Cloudinary'))
          }
        }
      ).end(buffer)
    })

    const result = uploadResult as {
      secure_url: string;
      public_id: string;
      eager?: Array<{ secure_url: string; transformation: string }>;
    }

    // Generate URLs for different sizes using Cloudinary's URL transformation
    const baseUrl = result.secure_url.replace(/\/v\d+\//, '/').replace(/\.[^.]+$/, '')
    const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME

    const generateUrl = (transformation: string) => {
      return `https://res.cloudinary.com/${cloudName}/image/upload/${transformation}/${result.public_id}.webp`
    }

    return NextResponse.json({
      success: true,
      url: result.secure_url, // Original high-quality image
      public_id: result.public_id,
      filename: result.public_id,
      // Provide multiple size variants for responsive loading
      variants: {
        thumbnail: generateUrl('c_fill,g_face:center,h_160,q_90,w_160,f_webp'),
        small: generateUrl('c_fill,g_face:center,h_320,q_92,w_320,f_webp'),
        medium: generateUrl('c_fill,g_face:center,h_640,q_95,w_640,f_webp'),
        large: generateUrl('c_fill,g_face:center,h_800,q_95,w_800,f_webp'),
        // High DPI variants for retina displays
        thumbnail_2x: generateUrl('c_fill,g_face:center,h_320,q_90,w_320,f_webp'),
        small_2x: generateUrl('c_fill,g_face:center,h_640,q_92,w_640,f_webp'),
        medium_2x: generateUrl('c_fill,g_face:center,h_1280,q_95,w_1280,f_webp')
      }
    })

  } catch (error) {
    console.error('Error uploading file to Cloudinary:', error)
    return NextResponse.json(
      { error: 'Failed to upload file to cloud storage' },
      { status: 500 }
    )
  }
}

// DELETE - Remove profile picture from Cloudinary
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const publicId = searchParams.get('public_id')

    if (!publicId) {
      return NextResponse.json({ error: 'Public ID is required' }, { status: 400 })
    }

    // Delete from Cloudinary
    const result = await cloudinary.uploader.destroy(publicId)

    if (result.result === 'ok') {
      return NextResponse.json({
        success: true,
        message: 'Profile picture deleted successfully from cloud storage'
      })
    } else {
      return NextResponse.json({
        error: 'Failed to delete image from cloud storage'
      }, { status: 400 })
    }

  } catch (error) {
    console.error('Error deleting file from Cloudinary:', error)
    return NextResponse.json(
      { error: 'Failed to delete file from cloud storage' },
      { status: 500 }
    )
  }
}
