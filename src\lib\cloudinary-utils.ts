/**
 * Cloudinary Image Optimization Utilities
 * Professional image transformation and optimization for Tindahan Store
 */

export interface ImageVariants {
  thumbnail: string
  small: string
  medium: string
  large: string
  thumbnail_2x: string
  small_2x: string
  medium_2x: string
}

/**
 * Extract Cloudinary public ID from a Cloudinary URL
 */
export function extractPublicId(cloudinaryUrl: string): string | null {
  try {
    // Match Cloudinary URL pattern and extract public ID
    const match = cloudinaryUrl.match(/\/(?:v\d+\/)?([^/.]+)(?:\.[^.]+)?$/)
    return match ? match[1] : null
  } catch {
    return null
  }
}

/**
 * Generate optimized Cloudinary URLs for different sizes and use cases
 */
export function generateImageVariants(
  cloudinaryUrl: string,
  cloudName?: string
): ImageVariants | null {
  if (!cloudinaryUrl || !cloudName) return null

  const publicId = extractPublicId(cloudinaryUrl)
  if (!publicId) return null

  const baseUrl = `https://res.cloudinary.com/${cloudName}/image/upload`

  return {
    // Standard variants
    thumbnail: `${baseUrl}/c_fill,g_face:center,h_160,q_90,w_160,f_webp/${publicId}.webp`,
    small: `${baseUrl}/c_fill,g_face:center,h_320,q_92,w_320,f_webp/${publicId}.webp`,
    medium: `${baseUrl}/c_fill,g_face:center,h_640,q_95,w_640,f_webp/${publicId}.webp`,
    large: `${baseUrl}/c_fill,g_face:center,h_800,q_95,w_800,f_webp/${publicId}.webp`,
    
    // High DPI variants for retina displays
    thumbnail_2x: `${baseUrl}/c_fill,g_face:center,h_320,q_90,w_320,f_webp/${publicId}.webp`,
    small_2x: `${baseUrl}/c_fill,g_face:center,h_640,q_92,w_640,f_webp/${publicId}.webp`,
    medium_2x: `${baseUrl}/c_fill,g_face:center,h_1280,q_95,w_1280,f_webp/${publicId}.webp`
  }
}

/**
 * Get optimal image URL based on size requirements and device capabilities
 */
export function getOptimalImageUrl(
  originalUrl: string,
  variants: ImageVariants | null,
  size: 'sm' | 'md' | 'lg' | 'xl' = 'md',
  cloudName?: string
): string {
  if (!originalUrl) return ''

  // Generate variants if not provided
  const imageVariants = variants || generateImageVariants(originalUrl, cloudName)
  if (!imageVariants) return originalUrl

  // Size to variant mapping
  const sizeVariantMap = {
    sm: 'thumbnail' as keyof ImageVariants,
    md: 'thumbnail' as keyof ImageVariants,
    lg: 'small' as keyof ImageVariants,
    xl: 'medium' as keyof ImageVariants
  }

  const variant = sizeVariantMap[size]
  const isHighDPI = typeof window !== 'undefined' && window.devicePixelRatio > 1

  // Use 2x variant for high DPI displays when available
  if (isHighDPI) {
    const highDPIVariant = `${variant}_2x` as keyof ImageVariants
    if (imageVariants[highDPIVariant]) {
      return imageVariants[highDPIVariant]
    }
  }

  return imageVariants[variant] || originalUrl
}

/**
 * Generate a blur placeholder for better loading experience
 */
export function generateBlurPlaceholder(
  cloudinaryUrl: string,
  cloudName?: string
): string {
  if (!cloudinaryUrl || !cloudName) {
    // Default blur placeholder
    return "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
  }

  const publicId = extractPublicId(cloudinaryUrl)
  if (!publicId) return "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="

  // Generate a very small, blurred version for placeholder
  return `https://res.cloudinary.com/${cloudName}/image/upload/c_fill,g_face:center,h_20,q_auto:low,w_20,e_blur:1000,f_auto/${publicId}`
}

/**
 * Validate if a URL is a Cloudinary URL
 */
export function isCloudinaryUrl(url: string): boolean {
  return url.includes('cloudinary.com') || url.includes('res.cloudinary.com')
}

/**
 * Get Cloudinary cloud name from environment
 */
export function getCloudName(): string | undefined {
  return process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME
}

/**
 * Enhanced image loading with progressive enhancement
 */
export interface OptimizedImageProps {
  src: string
  alt: string
  size: 'sm' | 'md' | 'lg' | 'xl'
  variants?: ImageVariants
  priority?: boolean
  className?: string
  onLoad?: () => void
  onError?: () => void
}

/**
 * Get responsive image props for Next.js Image component
 */
export function getResponsiveImageProps(props: OptimizedImageProps) {
  const { src, size, variants, priority = false } = props
  const cloudName = getCloudName()
  
  const optimizedSrc = getOptimalImageUrl(src, variants, size, cloudName)
  const blurDataURL = isCloudinaryUrl(src) 
    ? generateBlurPlaceholder(src, cloudName)
    : undefined

  const sizeMap = {
    sm: { width: 64, height: 64 },
    md: { width: 96, height: 96 },
    lg: { width: 128, height: 128 },
    xl: { width: 192, height: 192 }
  }

  return {
    src: optimizedSrc,
    ...sizeMap[size],
    quality: 95,
    priority,
    placeholder: blurDataURL ? 'blur' as const : undefined,
    blurDataURL,
    sizes: `${sizeMap[size].width}px`
  }
}
