'use client'

import { useState } from 'react'
import Image from 'next/image'
import { User } from 'lucide-react'

import { 
  ImageVariants, 
  getOptimalImageUrl, 
  generateBlurPlaceholder, 
  getCloudName,
  isCloudinaryUrl 
} from '@/lib/cloudinary-utils'

interface EnhancedImageProps {
  src: string
  alt: string
  size: 'sm' | 'md' | 'lg' | 'xl'
  variants?: ImageVariants
  priority?: boolean
  className?: string
  fallbackInitials?: string
  showLoadingSpinner?: boolean
  onLoad?: () => void
  onError?: () => void
}

const sizeClasses = {
  sm: 'w-16 h-16',
  md: 'w-24 h-24',
  lg: 'w-32 h-32',
  xl: 'w-48 h-48'
}

const iconSizes = {
  sm: 'h-6 w-6',
  md: 'h-8 w-8',
  lg: 'h-12 w-12',
  xl: 'h-16 w-16'
}

const sizeMap = {
  sm: { width: 64, height: 64 },
  md: { width: 96, height: 96 },
  lg: { width: 128, height: 128 },
  xl: { width: 192, height: 192 }
}

export default function EnhancedImage({
  src,
  alt,
  size,
  variants,
  priority = false,
  className = '',
  fallbackInitials,
  showLoadingSpinner = true,
  onLoad,
  onError
}: EnhancedImageProps) {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  const handleImageLoad = () => {
    setImageLoading(false)
    onLoad?.()
  }

  const handleImageError = () => {
    setImageError(true)
    setImageLoading(false)
    onError?.()
  }

  const getOptimizedImageUrl = (): string => {
    if (!src) return ''
    return getOptimalImageUrl(src, variants, size, getCloudName())
  }

  const getBlurPlaceholder = (): string | undefined => {
    if (!src || !isCloudinaryUrl(src)) return undefined
    return generateBlurPlaceholder(src, getCloudName())
  }

  const generateInitials = (): string => {
    if (fallbackInitials) return fallbackInitials
    
    const words = alt.split(' ')
    if (words.length >= 2) {
      return `${words[0]?.charAt(0) || ''}${words[1]?.charAt(0) || ''}`.toUpperCase()
    }
    return alt.charAt(0).toUpperCase()
  }

  const getAvatarColor = (): string => {
    const colors = [
      'bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-pink-500',
      'bg-indigo-500', 'bg-yellow-500', 'bg-red-500', 'bg-gray-500'
    ]
    const charCode = alt.charCodeAt(0) || 0
    return colors[charCode % colors.length] || 'bg-gray-500'
  }

  const shouldShowImage = src && !imageError

  return (
    <div className={`relative ${sizeClasses[size]} ${className}`}>
      <div className="w-full h-full rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700 relative">
        {shouldShowImage ? (
          <>
            {/* Loading spinner */}
            {imageLoading && showLoadingSpinner && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700 z-10">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              </div>
            )}
            
            {/* Optimized image */}
            <Image
              src={getOptimizedImageUrl()}
              alt={alt}
              width={sizeMap[size].width}
              height={sizeMap[size].height}
              className={`w-full h-full object-cover transition-all duration-300 ${
                imageLoading ? 'opacity-0' : 'opacity-100'
              }`}
              onLoad={handleImageLoad}
              onError={handleImageError}
              priority={priority}
              quality={95}
              placeholder={getBlurPlaceholder() ? "blur" : undefined}
              blurDataURL={getBlurPlaceholder()}
              sizes={`${sizeMap[size].width}px`}
            />
          </>
        ) : (
          /* Fallback avatar with initials */
          <div
            className={`w-full h-full flex items-center justify-center text-white font-semibold ${getAvatarColor()}`}
          >
            {generateInitials() ? (
              <span className={`${
                size === 'xl' ? 'text-2xl' : 
                size === 'lg' ? 'text-lg' : 
                size === 'md' ? 'text-sm' : 
                'text-xs'
              }`}>
                {generateInitials()}
              </span>
            ) : (
              <User className={iconSizes[size]} />
            )}
          </div>
        )}
      </div>
    </div>
  )
}
