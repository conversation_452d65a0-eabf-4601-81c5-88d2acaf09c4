-- =====================================================
-- ENHANCE PROFILE PICTURES - DATABASE IMPROVEMENTS
-- =====================================================
-- Professional enhancement for profile picture quality and management
-- This script adds support for image variants and better quality handling
--
-- 🎯 ENHANCEMENTS:
-- ✅ Add image variants storage for responsive loading
-- ✅ Add image metadata fields for better optimization
-- ✅ Create indexes for better performance
-- ✅ Add validation functions for image URLs
-- ✅ Backward compatible with existing data
-- ✅ Production-safe with conflict handling
-- =====================================================

-- Add new columns for enhanced image support (safe to run multiple times)
DO $$
BEGIN
    -- Add image variants column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'customers' 
        AND column_name = 'profile_picture_variants'
    ) THEN
        ALTER TABLE customers ADD COLUMN profile_picture_variants JSONB;
        RAISE NOTICE '✅ Added profile_picture_variants column to customers table';
    ELSE
        RAISE NOTICE '⚠️ Column profile_picture_variants already exists in customers table';
    END IF;

    -- Add image metadata column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'customers' 
        AND column_name = 'profile_picture_metadata'
    ) THEN
        ALTER TABLE customers ADD COLUMN profile_picture_metadata JSONB;
        RAISE NOTICE '✅ Added profile_picture_metadata column to customers table';
    ELSE
        RAISE NOTICE '⚠️ Column profile_picture_metadata already exists in customers table';
    END IF;

    -- Add image upload timestamp if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'customers' 
        AND column_name = 'profile_picture_uploaded_at'
    ) THEN
        ALTER TABLE customers ADD COLUMN profile_picture_uploaded_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE '✅ Added profile_picture_uploaded_at column to customers table';
    ELSE
        RAISE NOTICE '⚠️ Column profile_picture_uploaded_at already exists in customers table';
    END IF;
END $$;

-- =====================================================
-- VALIDATION FUNCTIONS FOR IMAGE URLS
-- =====================================================

-- Function to validate Cloudinary URLs
CREATE OR REPLACE FUNCTION validate_cloudinary_url(url TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Check if URL is null or empty
    IF url IS NULL OR LENGTH(TRIM(url)) = 0 THEN
        RETURN TRUE; -- Allow empty URLs
    END IF;

    -- Check if URL is a valid Cloudinary URL
    IF url ~ '^https://res\.cloudinary\.com/[^/]+/image/upload/' THEN
        RETURN TRUE;
    END IF;

    -- Check for legacy Cloudinary URLs
    IF url ~ '^https://cloudinary\.com/' THEN
        RETURN TRUE;
    END IF;

    -- Allow other valid image URLs for flexibility
    IF url ~ '^https?://.*\.(jpg|jpeg|png|webp|gif)(\?.*)?$' THEN
        RETURN TRUE;
    END IF;

    RETURN FALSE;
END;
$$;

-- Function to validate image variants JSON structure
CREATE OR REPLACE FUNCTION validate_image_variants(variants JSONB)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Allow null variants
    IF variants IS NULL THEN
        RETURN TRUE;
    END IF;

    -- Check if variants has expected structure
    IF variants ? 'thumbnail' AND variants ? 'small' AND variants ? 'medium' AND variants ? 'large' THEN
        -- Validate that each variant is a valid URL
        IF validate_cloudinary_url(variants->>'thumbnail') AND
           validate_cloudinary_url(variants->>'small') AND
           validate_cloudinary_url(variants->>'medium') AND
           validate_cloudinary_url(variants->>'large') THEN
            RETURN TRUE;
        END IF;
    END IF;

    RETURN FALSE;
END;
$$;

-- =====================================================
-- ADD VALIDATION CONSTRAINTS
-- =====================================================

-- Add constraint for profile picture URL validation
DO $$
BEGIN
    -- Drop existing constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'customers' 
        AND constraint_name = 'customers_profile_picture_url_valid'
    ) THEN
        ALTER TABLE customers DROP CONSTRAINT customers_profile_picture_url_valid;
    END IF;

    -- Add new constraint
    ALTER TABLE customers ADD CONSTRAINT customers_profile_picture_url_valid 
    CHECK (validate_cloudinary_url(profile_picture_url));
    
    RAISE NOTICE '✅ Added profile picture URL validation constraint';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '⚠️ Could not add profile picture URL constraint: %', SQLERRM;
END $$;

-- Add constraint for image variants validation
DO $$
BEGIN
    -- Drop existing constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'customers' 
        AND constraint_name = 'customers_profile_picture_variants_valid'
    ) THEN
        ALTER TABLE customers DROP CONSTRAINT customers_profile_picture_variants_valid;
    END IF;

    -- Add new constraint
    ALTER TABLE customers ADD CONSTRAINT customers_profile_picture_variants_valid 
    CHECK (validate_image_variants(profile_picture_variants));
    
    RAISE NOTICE '✅ Added profile picture variants validation constraint';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '⚠️ Could not add profile picture variants constraint: %', SQLERRM;
END $$;

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Index for profile picture queries
CREATE INDEX IF NOT EXISTS idx_customers_profile_picture_url 
ON customers(profile_picture_url) 
WHERE profile_picture_url IS NOT NULL;

-- Index for profile picture variants
CREATE INDEX IF NOT EXISTS idx_customers_profile_picture_variants 
ON customers USING gin(profile_picture_variants) 
WHERE profile_picture_variants IS NOT NULL;

-- Index for profile picture upload timestamp
CREATE INDEX IF NOT EXISTS idx_customers_profile_picture_uploaded_at 
ON customers(profile_picture_uploaded_at) 
WHERE profile_picture_uploaded_at IS NOT NULL;

-- =====================================================
-- TRIGGER FOR AUTOMATIC TIMESTAMP UPDATE
-- =====================================================

-- Function to update profile picture timestamp
CREATE OR REPLACE FUNCTION update_profile_picture_timestamp()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Update timestamp when profile picture URL changes
    IF OLD.profile_picture_url IS DISTINCT FROM NEW.profile_picture_url THEN
        NEW.profile_picture_uploaded_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$$;

-- Create trigger for profile picture timestamp updates
DROP TRIGGER IF EXISTS update_profile_picture_timestamp_trigger ON customers;
CREATE TRIGGER update_profile_picture_timestamp_trigger
    BEFORE UPDATE ON customers
    FOR EACH ROW
    EXECUTE FUNCTION update_profile_picture_timestamp();

-- =====================================================
-- UTILITY FUNCTIONS FOR IMAGE MANAGEMENT
-- =====================================================

-- Function to get customer with optimized image data
CREATE OR REPLACE FUNCTION get_customer_with_images(
    p_customer_name TEXT,
    p_customer_family_name TEXT
)
RETURNS TABLE(
    id UUID,
    customer_name VARCHAR(255),
    customer_family_name VARCHAR(255),
    profile_picture_url TEXT,
    profile_picture_public_id TEXT,
    profile_picture_variants JSONB,
    profile_picture_metadata JSONB,
    profile_picture_uploaded_at TIMESTAMP WITH TIME ZONE,
    phone_number VARCHAR(20),
    address TEXT,
    birth_date DATE,
    birth_place VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.customer_name,
        c.customer_family_name,
        c.profile_picture_url,
        c.profile_picture_public_id,
        c.profile_picture_variants,
        c.profile_picture_metadata,
        c.profile_picture_uploaded_at,
        c.phone_number,
        c.address,
        c.birth_date,
        c.birth_place,
        c.notes,
        c.created_at,
        c.updated_at
    FROM customers c
    WHERE c.customer_name = p_customer_name 
    AND c.customer_family_name = p_customer_family_name;
END;
$$;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '🎉 =====================================================';
    RAISE NOTICE '🎉 PROFILE PICTURE ENHANCEMENTS COMPLETE!';
    RAISE NOTICE '🎉 =====================================================';
    RAISE NOTICE '';
    RAISE NOTICE '✅ ENHANCEMENTS APPLIED:';
    RAISE NOTICE '   📊 Added image variants storage (JSONB)';
    RAISE NOTICE '   📋 Added image metadata support';
    RAISE NOTICE '   ⏰ Added upload timestamp tracking';
    RAISE NOTICE '   🔍 Added performance indexes';
    RAISE NOTICE '   ✅ Added URL validation functions';
    RAISE NOTICE '   🔧 Added automatic timestamp triggers';
    RAISE NOTICE '   🛡️ Added data validation constraints';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 READY FOR ENHANCED PROFILE PICTURES!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 NEW FEATURES:';
    RAISE NOTICE '   • Responsive image variants for better quality';
    RAISE NOTICE '   • Automatic upload timestamp tracking';
    RAISE NOTICE '   • Enhanced URL validation';
    RAISE NOTICE '   • Better performance with specialized indexes';
    RAISE NOTICE '   • Metadata storage for image optimization';
    RAISE NOTICE '';
    RAISE NOTICE '🔒 PRODUCTION-SAFE: Backward compatible with existing data';
END $$;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify new columns were added
SELECT 
    'Column Verification' as check_type,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_name = 'customers'
AND column_name IN ('profile_picture_variants', 'profile_picture_metadata', 'profile_picture_uploaded_at')
ORDER BY column_name;

-- Verify constraints were added
SELECT 
    'Constraint Verification' as check_type,
    constraint_name,
    constraint_type
FROM information_schema.table_constraints
WHERE table_name = 'customers'
AND constraint_name LIKE '%profile_picture%'
ORDER BY constraint_name;

-- Verify indexes were created
SELECT 
    'Index Verification' as check_type,
    indexname,
    indexdef
FROM pg_indexes
WHERE tablename = 'customers'
AND indexname LIKE '%profile_picture%'
ORDER BY indexname;
